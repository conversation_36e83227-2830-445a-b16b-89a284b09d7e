generator client {
    provider = "prisma-client-js"
}

datasource db {
    provider = "mongodb"
    url      = env("DATABASE_URL")
}

// --- CORE USER & SUBSCRIPTION MODELS ---
model User {
    id                String             @id @default(auto()) @map("_id") @db.ObjectId
    name              String
    email             String             @unique
    isVerified        Boolean            @default(false)
    createdAt         DateTime           @default(now())
    updatedAt         DateTime           @default(now()) @updatedAt
    role              UserRole           @default(USER)
    otpCodes          OtpCode[]
    subscription      Subscription?
    testAttempts      TestAttempt[]
    courses           UserCourse[]
    transactions      Transaction[]
    notifications     Notification[]
    deletedAt         DateTime? // For soft deletes
    practiceSessions  PracticeSession[]
    practiceResponses PracticeResponse[]
    questionResponses QuestionResponse[]

    // --- Google OAuth Fields ---
    googleId           String? @unique
    googleAccessToken  String?
    googleRefreshToken String?

    // --- Apple OAuth Fields ---
    appleId           String? @unique // Unique ID from Apple (subject 'sub' claim)
    appleRefreshToken String? // Needed to revoke tokens or potentially get new access tokens
    appleEmail        String? // The email Apple provides (could be relay email)
    // You might also want to store the family name and given name if provided
    appleGivenName    String?
    appleFamilyName   String?

    profilePictureUrl   String? // Consolidate profile picture URL for any provider
    provider            AuthProviders         @default(EMAIL_OTP) // Which provider the user last used to login
    CourseReview        CourseReview[]
    UserLessonProgress  UserLessonProgress[]
    UserSectionProgress UserSectionProgress[]
    userResponses       UserResponse[]
}

// Practice session model for tracking daily practice
model PracticeSession {
    id                String             @id @default(auto()) @map("_id") @db.ObjectId
    userId            String             @db.ObjectId
    user              User               @relation(fields: [userId], references: [id], onDelete: Cascade)
    sessionDate       DateTime // Date of practice session (normalized to start of day)
    totalQuestions    Int                @default(0)
    correctAnswers    Int                @default(0)
    totalTimeSpent    Int                @default(0) // in seconds
    createdAt         DateTime           @default(now())
    updatedAt         DateTime           @updatedAt
    practiceResponses PracticeResponse[]

    @@unique([userId, sessionDate])
}

// Individual practice response model
model PracticeResponse {
    id                String              @id @default(auto()) @map("_id") @db.ObjectId
    userId            String              @db.ObjectId
    user              User                @relation(fields: [userId], references: [id], onDelete: Cascade)
    questionId        String              @db.ObjectId
    question          Question            @relation(fields: [questionId], references: [id], onDelete: Cascade)
    practiceSessionId String              @db.ObjectId
    practiceSession   PracticeSession     @relation(fields: [practiceSessionId], references: [id], onDelete: Cascade)
    questionType      PteQuestionTypeName
    userResponse      Json // Store the user's response (text, selected options, etc.)
    timeTakenSeconds  Int                 @default(0)
    isCorrect         Boolean             @default(false)
    score             Float               @default(0) // Score for this question (0-1 or 0-100 depending on scoring system)
    createdAt         DateTime            @default(now())

    @@index([userId, questionType])
    @@index([userId, createdAt])
    @@index([practiceSessionId])
}

// New OTP Code model
model OtpCode {
    id        String   @id @default(auto()) @map("_id") @db.ObjectId
    email     String
    code      String
    type      OtpType
    expiresAt DateTime
    used      Boolean  @default(false)
    user      User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId    String?  @db.ObjectId
    createdAt DateTime @default(now())

    @@index([email, type])
    @@index([expiresAt])
}

model Subscription {
    id                   String           @id @default(auto()) @map("_id") @db.ObjectId
    planName             SubscriptionPlan @default(FREE)
    price                Float
    durationInDays       Int // e.g., 30 for 30 days
    features             String[]
    user                 User             @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
    userId               String           @unique @db.ObjectId
    startDate            DateTime         @default(now())
    endDate              DateTime // Calculated based on startDate + durationInDays
    createdAt            DateTime         @default(now())
    updatedAt            DateTime         @updatedAt
    stripeSubscriptionId String?          @unique
    stripeCustomerId     String?

    // --- FIELDS FOR FREE TRIAL ---
    isTrial                 Boolean   @default(false) // True if this subscription record represents a free trial
    trialStartedAt          DateTime? // When the trial began
    trialEndsAt             DateTime? // When the trial will automatically expire/convert
    trialConvertedToPaid    Boolean?  @default(false) // True if the trial successfully converted to a paid plan
    trialCancellationReason String? // Optional: If user cancels trial, why?
    // Add a field to link to the actual paid subscription if it converts
    // convertedToSubscriptionId String? @db.ObjectId // If you create a new Subscription record upon conversion
    // convertedToSubscription   Subscription? @relation("TrialConversion", fields: [convertedToSubscriptionId], references: [id])
    // Instead of a separate record, you might just update the existing one (simpler)
}

// --- LEARNING CONTENT MODELS ---
model Course {
    id                    String          @id @default(auto()) @map("_id") @db.ObjectId
    title                 String
    description           String
    coursePreviewVideoUrl String?
    sections              CourseSection[]
    userCourses           UserCourse[]
    isFree                Boolean         @default(false)
    createdAt             DateTime        @default(now())
    updatedAt             DateTime        @updatedAt // Added updatedAt
    imageUrl              String? // Optional: Course image URL
    reviews               CourseReview[]
    averageRating         Float?          @default(0.0) // Can be updated by a trigger/cron job
    reviewCount           Int?            @default(0) // Can be updated by a trigger/cron job
    price                 Float? // Price of the course if sold individually
    currency              String?         @default("INR") // e.g., "USD", "INR"
    stripeProductId       String? // Stripe product ID for payments
    stripePriceId         String? // Stripe price ID for payments

    categoryIds String[] @db.ObjectId // NEW: Array of category ObjectId references
    // instructor    User? @relation(fields: [instructorId], references: [id]) // Uncomment if you add an instructor role
    // instructorId  String? @db.ObjectId
}

model CourseSection {
    id                  String                @id @default(auto()) @map("_id") @db.ObjectId
    title               String
    description         String?
    videoUrl            String? // Legacy field - for backward compatibility
    videoKey            String? // S3 key for secure video storage
    course              Course                @relation(fields: [courseId], references: [id], onDelete: Cascade)
    courseId            String                @db.ObjectId
    order               Int // To define the order of sections within a course
    lessons             Lesson[]
    createdAt           DateTime              @default(now()) // Added createdAt for content
    updatedAt           DateTime              @updatedAt // Added updatedAt for content
    UserSectionProgress UserSectionProgress[]
}

model Lesson {
    id                 String               @id @default(auto()) @map("_id") @db.ObjectId
    title              String
    description        String?
    videoUrl           String? // Legacy field - for backward compatibility
    videoKey           String? // S3 key for secure video storage
    textContent        String? // Any accompanying text/notes
    audioUrl           String? // For audio-only lessons
    section            CourseSection        @relation(fields: [sectionId], references: [id], onDelete: Cascade)
    sectionId          String               @db.ObjectId
    order              Int // Order of lessons within a section
    createdAt          DateTime             @default(now()) // Added createdAt for content
    updatedAt          DateTime             @updatedAt // Added updatedAt for content
    // questions     Question[] // If a lesson can contain practice questions directly
    UserLessonProgress UserLessonProgress[]
    LessonResource     LessonResource[]
}

model UserCourse {
    id          String    @id @default(auto()) @map("_id") @db.ObjectId
    user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId      String    @db.ObjectId
    course      Course    @relation(fields: [courseId], references: [id])
    courseId    String    @db.ObjectId
    progress    Float     @default(0.0) // Percentage of course completed
    completed   Boolean   @default(false)
    enrolledAt  DateTime  @default(now())
    completedAt DateTime? // When the user completed the course

    @@unique([userId, courseId]) // A user can only enroll in a course once
    @@index([userId])
    @@index([courseId])
}

model Category {
    id          String   @id @default(auto()) @map("_id") @db.ObjectId
    name        String   @unique
    slug        String   @unique // For friendly URLs (e.g., /courses/grammar)
    description String?
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt
    // No direct relation back to Course here
}

model CourseReview {
    id        String   @id @default(auto()) @map("_id") @db.ObjectId
    rating    Int // 1 to 5 stars
    comment   String?
    user      User     @relation(fields: [userId], references: [id])
    userId    String   @db.ObjectId
    course    Course   @relation(fields: [courseId], references: [id], onDelete: Cascade)
    courseId  String   @db.ObjectId
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@unique([userId, courseId]) // A user can review a course only once
    @@index([courseId, rating]) // For querying average ratings
}

model UserLessonProgress {
    id              String    @id @default(auto()) @map("_id") @db.ObjectId
    user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId          String    @db.ObjectId
    lesson          Lesson    @relation(fields: [lessonId], references: [id], onDelete: Cascade)
    lessonId        String    @db.ObjectId
    isCompleted     Boolean   @default(false)
    watchedDuration Float? // For video lessons, how many seconds watched
    completedAt     DateTime? // When the lesson was completed
    lastAccessedAt  DateTime  @default(now()) // When the lesson was last accessed
    createdAt       DateTime  @default(now())
    updatedAt       DateTime  @updatedAt

    @@unique([userId, lessonId]) // User can only have one progress record per lesson
    @@index([userId, isCompleted])
    @@index([userId, lastAccessedAt])
}

model UserSectionProgress {
    id             String        @id @default(auto()) @map("_id") @db.ObjectId
    user           User          @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId         String        @db.ObjectId
    section        CourseSection @relation(fields: [sectionId], references: [id], onDelete: Cascade)
    sectionId      String        @db.ObjectId
    isCompleted    Boolean       @default(false)
    completedAt    DateTime? // When the section was completed
    lastAccessedAt DateTime      @default(now()) // When the section was last accessed
    createdAt      DateTime      @default(now())
    updatedAt      DateTime      @updatedAt

    @@unique([userId, sectionId]) // User can only have one progress record per section
    @@index([userId, isCompleted])
    @@index([userId, lastAccessedAt])
}

model LessonResource {
    id        String   @id @default(auto()) @map("_id") @db.ObjectId
    name      String
    fileUrl   String // URL to the hosted file (PDF, DOCX, etc.)
    fileType  String? // e.g., "pdf", "docx", "zip"
    lesson    Lesson   @relation(fields: [lessonId], references: [id], onDelete: Cascade)
    lessonId  String   @db.ObjectId
    createdAt DateTime @default(now())
}

// --- PTE SIMULATION MODELS ---

// Defines the major sections of a PTE Test (e.g., Speaking & Writing, Reading, Listening)
model PteSection {
    id              String         @id @default(auto()) @map("_id") @db.ObjectId
    name            String         @unique // e.g., "Speaking & Writing", "Reading", "Listening"
    description     String?
    durationMinutes Int // Expected duration for this section in a real test
    questionTypes   QuestionType[] // All question types that belong to this section
    order           Int?
}

// Defines a specific type of question (e.g., Read Aloud, Essay, Multiple Choice Single Answer)
model QuestionType {
    id                      String              @id @default(auto()) @map("_id") @db.ObjectId
    name                    PteQuestionTypeName @unique // Using the specific enum for question types
    description             String? // How this question type works, general instructions
    pteSection              PteSection          @relation(fields: [pteSectionId], references: [id], onDelete: Cascade)
    pteSectionId            String              @db.ObjectId
    expectedTimePerQuestion Int? // Optional: average time for this question type (for guidance)
    questions               Question[] // All individual questions of this type
}

// Represents a specific instance of a question that can appear in a Test
model Question {
    id             String       @id @default(auto()) @map("_id") @db.ObjectId
    questionCode   String       @unique // For easy identification/referencing (e.g., "RA_001_Test1")
    questionType   QuestionType @relation(fields: [questionTypeId], references: [id])
    questionTypeId String       @db.ObjectId
   difficultyLevel DifficultyLevel @default(MEDIUM) // New field for difficulty
    // test           Test         @relation(fields: [testId], references: [id]) // Which test this question belongs to
    // testId         String       @db.ObjectId
    // orderInTest    Int // Order of question within a specific test

    // Question content fields - tailored for PTE Academic question types
    textContent String? // For Read Aloud, Summarize Written Text, Essay, Reading Fill in the Blanks, R&W Fill in the Blanks, Re-order Paragraphs, Highlight Incorrect Words
    audioUrl    String? // For Repeat Sentence, Re-tell Lecture, Answer Short Question, Summarize Spoken Text, Listening Fill in the Blanks, Highlight Correct Summary, Select Missing Word, Write from Dictation
    imageUrl    String? // For Describe Image (URL to the image)

    // Answer options/structure for various question types
    options        Json? // For Multiple Choice (Single/Multiple Answer), Highlight Correct Summary, Select Missing Word
    // Example for MCQs: [{ text: "Option A", isCorrect: true }, { text: "Option B", isCorrect: false }]
    correctAnswers Json? // For Fill in the Blanks (array of strings), Write from Dictation (string), Repeat Sentence (string), Re-order Paragraphs (array of strings/IDs in correct order), Answer Short Question (array of strings)
    // Example for Reading Fill in the Blanks (dropdowns): [{ gapId: "gap1", options: ["A", "B"], correctAnswer: "A" }]

    // Specific constraints/metadata for question types
    wordCountMin           Int? // For Essay, Summarize Written Text, Summarize Spoken Text (min word limit)
    wordCountMax           Int? // For Essay, Summarize Written Text, Summarize Spoken Text (max word limit)
    durationMillis         Int? // For audio/video duration, or time limit for recording response (e.g., for Read Aloud preparation time, recording time)
    // For Highlight Incorrect Words
    originalTextWithErrors String? // The text shown to the user with errors to highlight
    incorrectWords         Json? // e.g., ["word1", "word2"] - actual incorrect words in the original text to mark

    createdAt        DateTime           @default(now())
    updatedAt        DateTime           @updatedAt
    UserResponse     UserResponse[] // Inverse relation: a question can have many user responses across different attempts
    PracticeResponse PracticeResponse[] // Relation to practice responses
    QuestionResponse QuestionResponse[] // Relation to standalone question responses
    // Keeping this for now as it's not "non-logical", just potentially large if eager loaded.
    // @@index([testId])

    @@index([questionTypeId])
   @@index([difficultyLevel])
}

// Represents a full PTE Mock Test
model Test {
    id            String  @id @default(auto()) @map("_id") @db.ObjectId
    title         String // e.g., "PTE Mock Test 1 (Academic)"
    description   String?
    testType      String  @default("ACADEMIC") // Could be "ACADEMIC", "CORE" - helpful for filtering
    totalDuration Int // Total duration in minutes (e.g., 120 minutes)
    isFree        Boolean @default(false) // Whether this test is free or requires subscription/payment

    // Link questions to the test using a list of their IDs
    questionIds String[] @db.ObjectId
    // questions     Question[] // Questions belonging to this test

    testAttempts TestAttempt[]
    createdAt    DateTime      @default(now())
    updatedAt    DateTime      @updatedAt

    @@index([isFree])
    @@index([testType])
}

// Represents a user's attempt at a specific test
model TestAttempt {
    id                 String         @id @default(auto()) @map("_id") @db.ObjectId
    user               User           @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId             String         @db.ObjectId
    test               Test           @relation(fields: [testId], references: [id])
    testId             String         @db.ObjectId
    startedAt          DateTime       @default(now())
    completedAt        DateTime?
    overallScore       Int? // Final overall score (10-90)
    // Communicative skills scores (Keeping these here for easy access/display)
    speakingScore      Int? // 10-90
    writingScore       Int? // 10-90
    readingScore       Int? // 10-90
    listeningScore     Int? // 10-90
    // Enabling skills scores (PTE specific)
    grammarScore       Int? // 10-90
    oralFluencyScore   Int? // 10-90
    pronunciationScore Int? // 10-90
    vocabularyScore    Int? // 10-90
    discourseScore     Int? // 10-90 (for coherence/cohesion)
    spellingScore      Int? // 10-90
    // User's answers/responses for each question (removed for standalone question practice)
    feedback           AIReport? // One-to-one with AIReport
    rawPteScoreJson    Json? // To store the full detailed PTE score breakdown from AI if available
    status             String         @default("IN_PROGRESS") // IN_PROGRESS, COMPLETED, REVIEW_PENDING (if human review needed)
    timeTakenSeconds   Int? // Timer for the overall test/sections

    @@index([userId, testId])
    @@index([userId, startedAt])
    @@index([status])
}

// Stores the user's specific answer for individual questions (standalone practice)
model UserResponse {
    id         String   @id @default(auto()) @map("_id") @db.ObjectId
    user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId     String   @db.ObjectId
    question   Question @relation(fields: [questionId], references: [id])
    questionId String   @db.ObjectId

    // User's response data - highly dependent on question type
    textResponse     String? // For Essay, Summarize Written Text, Fill in the Blanks (typed answer)
    audioResponseUrl String? // For Read Aloud, Repeat Sentence, Describe Image, Re-tell Lecture, Answer Short Question (URL to user's recorded audio)
    selectedOptions  String[] // For Multiple Choice (Single/Multiple Answer), Highlight Correct Summary, Select Missing Word (array of selected option IDs or values)
    orderedItems     String[] // For Re-order Paragraphs (array of paragraph IDs/segments in user's chosen order)
    highlightedWords String[] // For Highlight Incorrect Words (array of words user highlighted)

    // Scoring specific to this question (can be derived from AIReport, or used for partial scoring)
    questionScore Int? // Score for this specific question (e.g., out of 1 or 2, depending on question type)
    isCorrect     Boolean? // For questions with clear correct/incorrect answers
    aiFeedback    String? // Direct AI feedback for this specific question (e.g., "Pronunciation clear but content slightly off")
    detailedAnalysis Json? // Detailed evaluation data including word analysis, scores, etc.

    timeTakenSeconds Int? // How long the user spent on this question
    createdAt        DateTime @default(now())
    updatedAt        DateTime @updatedAt

    @@index([userId])
    @@index([questionId])
    @@index([userId, questionId])
}

// New model for standalone question responses (not part of a test)
model QuestionResponse {
    id               String   @id @default(auto()) @map("_id") @db.ObjectId
    userId           String   @db.ObjectId
    user             User     @relation(fields: [userId], references: [id], onDelete: Cascade)
    questionId       String   @db.ObjectId
    question         Question @relation(fields: [questionId], references: [id], onDelete: Cascade)
    
    // User's response data
    textResponse     String?
    audioResponseUrl String?
    selectedOptions  String[]
    orderedItems     String[]
    highlightedWords String[]
    
    // AI Evaluation results
    score            Float    @default(0) // 0-100 score from AI evaluation
    isCorrect        Boolean  @default(false)
    aiFeedback       String? // AI-generated feedback
    detailedAnalysis Json? // Detailed analysis from AI
    suggestions      String[] // AI suggestions for improvement
    
    timeTakenSeconds Int      @default(0)
    createdAt        DateTime @default(now())
    updatedAt        DateTime @updatedAt

    @@index([userId, questionId])
    @@index([userId, createdAt])
    @@index([questionId])
}

// AI-generated report for a TestAttempt
model AIReport {
    id                  String      @id @default(auto()) @map("_id") @db.ObjectId
    testAttempt         TestAttempt @relation(fields: [testAttemptId], references: [id], onDelete: Cascade)
    testAttemptId       String      @unique @db.ObjectId // One-to-one
    overallSummary      String // Overall summary of performance
    strengths           String[] // Areas where the user performed well
    weaknesses          String[] // Areas needing improvement
    suggestions         String[] // Actionable advice for improvement
    // Detailed breakdown of scores for specific skills (Keeping these here as a comprehensive AI report)
    grammarScore        Int?
    oralFluencyScore    Int?
    pronunciationScore  Int?
    vocabularyScore     Int?
    discourseScore      Int?
    spellingScore       Int?
    // Detailed feedback for specific question types or sections
    sectionWiseFeedback Json? // e.g., { "Speaking & Writing": { fluency: "Good", pronunciation: "Needs improvement in 'th' sounds" } }
    createdAt           DateTime    @default(now())
    updatedAt           DateTime    @updatedAt
}

// --- TRANSACTION & NOTIFICATION MODELS ---
model Transaction {
    id            String        @id @default(auto()) @map("_id") @db.ObjectId
    user          User          @relation(fields: [userId], references: [id])
    userId        String        @db.ObjectId
    amount        Float
    paymentStatus PaymentStatus @default(PENDING)
    gateway       String // e.g., "Razorpay", "Stripe"
    transactionId String? // ID from the payment gateway
    orderId       String? // Your internal order ID if different from transactionId
    purchasedItem String? // What was purchased (e.g., "Premium Plan", "PTE Mock Test 3")
    createdAt     DateTime      @default(now())

    @@unique([transactionId, userId])
    @@index([gateway])
    @@index([paymentStatus])
    @@index([userId, createdAt])
}

model Notification {
    id        String   @id @default(auto()) @map("_id") @db.ObjectId
    userId    String   @db.ObjectId
    user      User     @relation(fields: [userId], references: [id])
    message   String
    type      String // e.g., "SYSTEM", "COURSE_UPDATE", "TEST_FEEDBACK"
    read      Boolean  @default(false)
    createdAt DateTime @default(now())

    @@index([userId, read])
    @@index([createdAt])
}

// Enums for better type safety and clarity
enum UserRole {
    USER
    ADMIN
}

enum PaymentStatus {
    PENDING
    SUCCESS
    FAILED
    REFUNDED
}

enum SubscriptionPlan {
    FREE
    BASIC
    PREMIUM
}

// Enum for PTE Academic Question Types (Crucial for simulation)
enum PteQuestionTypeName {
    // Speaking (AI Scored)
    READ_ALOUD
    REPEAT_SENTENCE
    DESCRIBE_IMAGE
    RE_TELL_LECTURE
    ANSWER_SHORT_QUESTION

    // Writing (AI Scored)
    SUMMARIZE_WRITTEN_TEXT
    WRITE_ESSAY

    // Reading
    READING_WRITING_FILL_IN_THE_BLANKS // Combined skill type
    MULTIPLE_CHOICE_MULTIPLE_ANSWERS_READING // Specific to Reading
    RE_ORDER_PARAGRAPHS
    READING_FILL_IN_THE_BLANKS // Specific to Reading
    MULTIPLE_CHOICE_SINGLE_ANSWER_READING // Specific to Reading

    // Listening
    SUMMARIZE_SPOKEN_TEXT // AI Scored
    MULTIPLE_CHOICE_MULTIPLE_ANSWERS_LISTENING // Specific to Listening
    LISTENING_FILL_IN_THE_BLANKS // Specific to Listening
    HIGHLIGHT_CORRECT_SUMMARY
    MULTIPLE_CHOICE_SINGLE_ANSWER_LISTENING // Specific to Listening
    SELECT_MISSING_WORD
    HIGHLIGHT_INCORRECT_WORDS
    WRITE_FROM_DICTATION
}

enum AuthProviders {
    EMAIL_OTP
    GOOGLE
    APPLE
}

enum OtpType {
    LOGIN
    REGISTRATION
    PASSWORD_RESET
}

// Enum for question difficulty levels
enum DifficultyLevel {
    EASY
    MEDIUM
    HARD
}